GEM
  remote: https://rubygems.org/
  specs:
    actioncable (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
    actionmailer (8.0.2)
      actionpack (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.2)
      actionview (= 8.0.2)
      activesupport (= 8.0.2)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.2)
      actionpack (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.2)
      activesupport (= 8.0.2)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.3.6)
    activemodel (8.0.2)
      activesupport (= 8.0.2)
    activerecord (8.0.2)
      activemodel (= 8.0.2)
      activesupport (= 8.0.2)
      timeout (>= 0.4.0)
    activestorage (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activesupport (= 8.0.2)
      marcel (~> 1.0)
    activesupport (8.0.2)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    annotaterb (4.14.0)
    ast (2.4.3)
    backport (1.2.0)
    base64 (0.2.0)
    bcrypt_pbkdf (1.1.1)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    bindex (0.8.1)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    childprocess (5.1.0)
      logger (~> 1.5)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    cssbundling-rails (1.4.3)
      railties (>= 6.0.0)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    diff-lcs (1.6.2)
    docile (1.4.1)
    dotenv (3.1.8)
    drb (2.2.3)
    ed25519 (1.4.0)
    erb (5.0.1)
    erubi (1.13.1)
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jaro_winkler (1.6.1)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jsbundling-rails (1.3.1)
      railties (>= 6.0.0)
    json (2.12.0)
    kamal (2.6.1)
      activesupport (>= 7.0)
      base64 (~> 0.2)
      bcrypt_pbkdf (~> 1.0)
      concurrent-ruby (~> 1.2)
      dotenv (~> 3.1)
      ed25519 (~> 1.4)
      net-ssh (~> 7.3)
      sshkit (>= 1.23.0, < 2.0)
      thor (~> 1.3)
      zeitwerk (>= 2.6.18, < 3.0)
    kramdown (2.5.1)
      rexml (>= 3.3.9)
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    letter_opener_web (3.0.0)
      actionmailer (>= 6.1)
      letter_opener (~> 1.9)
      railties (>= 6.1)
      rexml
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_mime (1.1.5)
    minitest (5.25.5)
    msgpack (1.8.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-musl)
      racc (~> 1.4)
    observer (0.1.2)
    ostruct (0.6.1)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.15)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.2)
      actioncable (= 8.0.2)
      actionmailbox (= 8.0.2)
      actionmailer (= 8.0.2)
      actionpack (= 8.0.2)
      actiontext (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activemodel (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      bundler (>= 1.15.0)
      railties (= 8.0.2)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rbs (3.9.4)
      logger
    rdoc (6.14.0)
      erb
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    reverse_markdown (3.0.0)
      nokogiri
    rexml (3.4.1)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (8.0.0)
      actionpack (>= 7.2)
      activesupport (>= 7.2)
      railties (>= 7.2)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.3)
    rubocop (1.75.6)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-factory_bot (2.27.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.32.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-rspec_rails (2.31.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
      rubocop-rspec (~> 3.5)
    ruby-progressbar (1.13.0)
    securerandom (0.4.1)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    solargraph (0.54.5)
      backport (~> 1.2)
      benchmark (~> 0.4)
      bundler (~> 2.0)
      diff-lcs (~> 1.4)
      jaro_winkler (~> 1.6, >= 1.6.1)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.1)
      logger (~> 1.6)
      observer (~> 0.1)
      ostruct (~> 0.6)
      parser (~> 3.0)
      rbs (~> 3.3)
      reverse_markdown (~> 3.0)
      rubocop (~> 1.38)
      thor (~> 1.0)
      tilt (~> 2.0)
      yard (~> 0.9, >= 0.9.24)
      yard-solargraph (~> 0.1)
    solargraph-rails (1.1.0)
      activesupport
      solargraph
    sshkit (1.24.0)
      base64
      logger
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    thor (1.3.2)
    thruster (0.1.13)
    thruster (0.1.13-aarch64-linux)
    thruster (0.1.13-arm64-darwin)
    thruster (0.1.13-x86_64-linux)
    tilt (2.6.0)
    timeout (0.4.3)
    turbo-rails (2.0.13)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    yard (0.9.37)
    yard-solargraph (0.1.0)
      yard (~> 0.9)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin-24
  x86_64-linux
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  annotaterb
  bootsnap
  brakeman
  cssbundling-rails
  debug
  factory_bot_rails
  faker
  faraday
  jbuilder
  jsbundling-rails
  kamal
  letter_opener_web (~> 3.0)
  pg (~> 1.1)
  propshaft
  puma (>= 5.0)
  rails (~> 8.0.2)
  rspec-rails
  rubocop
  rubocop-factory_bot
  rubocop-performance
  rubocop-rails
  rubocop-rspec
  rubocop-rspec_rails
  shoulda-matchers
  simplecov
  solargraph
  solargraph-rails
  stimulus-rails
  thruster
  turbo-rails
  tzinfo-data
  web-console

BUNDLED WITH
   2.6.8
