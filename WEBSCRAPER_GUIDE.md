# WebScraper Mistral Agent Guide

## Overview

The **WebScraper Mistral** agent is a specialized AI assistant that can analyze any website and answer questions about its content. It automatically detects URLs in your messages, scrapes the websites, and uses that information to provide detailed, accurate responses.

## Features

### 🔍 **Automatic URL Detection**
- Simply include any URL in your message
- The agent automatically detects and scrapes the website
- No special commands needed

### 🧠 **Real-time Website Analysis**
- Extracts page titles, content, and structure
- Analyzes meta descriptions and keywords
- Identifies key headings and sections
- Provides content summaries

### 💬 **Intelligent Responses**
- Answers questions based on actual website content
- Cites specific information from the scraped pages
- Provides detailed analysis and insights
- Supports markdown formatting in responses

## How to Use

### 1. **Select the WebScraper Mistral Agent**
- Go to the chat interface
- Choose "WebScraper Mistral" from the available agents
- Look for the purple "WS" avatar

### 2. **Send Messages with URLs**
Simply include URLs in your messages and ask questions:

```
Analyze https://example.com and tell me what it's about
```

```
What are the main features mentioned on https://product-website.com?
```

```
Compare the content of https://site1.com and https://site2.com
```

### 3. **Ask Follow-up Questions**
After the initial scraping, you can ask detailed questions:

```
What pricing information is available on that website?
```

```
Who is the target audience for this product?
```

```
What are the key benefits mentioned?
```

## Example Use Cases

### 📊 **Website Analysis**
- "Analyze https://company.com and summarize their business model"
- "What services does https://agency.com offer?"

### 🔍 **Content Research**
- "Extract the key points from https://blog-post.com"
- "What are the main arguments in https://article.com?"

### 🆚 **Comparison**
- "Compare the features of https://product1.com vs https://product2.com"
- "How do these two websites differ in their approach?"

### 📈 **Market Research**
- "What can you tell me about https://competitor.com's pricing strategy?"
- "Analyze the positioning of https://startup.com"

## Technical Details

### **Supported Content Types**
- HTML web pages
- Blog posts and articles
- Product pages
- Company websites
- Documentation sites

### **Extracted Information**
- Page titles and headings
- Main content text
- Meta descriptions
- Keywords and tags
- Content structure

### **Error Handling**
- Graceful handling of inaccessible websites
- Timeout protection (30 seconds)
- Network error recovery
- Clear error messages

## Tips for Best Results

### ✅ **Do:**
- Use specific, well-formed URLs
- Ask clear, focused questions
- Include context about what you're looking for
- Use multiple URLs for comparison tasks

### ❌ **Avoid:**
- Broken or invalid URLs
- URLs requiring authentication
- Very large websites (may timeout)
- URLs with heavy JavaScript content

## Privacy & Security

- **No Data Storage**: Website content is not stored in our database
- **Temporary Processing**: Content is only used for the current conversation
- **Respectful Scraping**: Follows standard web scraping etiquette
- **Timeout Protection**: Prevents hanging on slow websites

## Troubleshooting

### **"Failed to access website" Error**
- Check if the URL is correct and accessible
- Ensure the website is publicly available
- Try again if it was a temporary network issue

### **"Website took too long to respond" Error**
- The website may be slow or overloaded
- Try again later
- Consider using a different URL for the same content

### **Incomplete Content**
- Some websites may block automated access
- JavaScript-heavy sites may not render fully
- Try finding alternative sources for the same information

## Getting Started

1. **Start a new chat** with WebScraper Mistral
2. **Paste a URL** in your message with a question
3. **Wait for analysis** (you'll see a typing indicator)
4. **Review the results** and ask follow-up questions
5. **Explore more websites** by adding new URLs

The WebScraper Mistral agent makes web research effortless by bringing website content directly into your conversation!
