# frozen_string_literal: true

# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

# Create default agents (skip if they already exist)
Rails.logger.debug 'Creating agents...'

agents = [
  {
    name: 'Llama 3.2',
    description: 'Latest Llama model with excellent general capabilities. Great for conversations and problem-solving.',
    avatar_url: 'https://ui-avatars.com/api/?name=L3.2&background=0ea5e9&color=fff&size=40&rounded=true',
    configuration: {
      model: 'llama3.2',
      system_prompt: 'You are a helpful AI assistant. Provide clear, accurate, and helpful responses.'
    }
  },
  {
    name: 'Llama 3.1',
    description: 'Powerful Llama model with strong reasoning capabilities. Excellent for complex tasks.',
    avatar_url: 'https://ui-avatars.com/api/?name=L3.1&background=059669&color=fff&size=40&rounded=true',
    configuration: {
      model: 'llama3.1',
      system_prompt: 'You are an intelligent AI assistant focused on providing detailed and accurate information.'
    }
  },
  {
    name: 'Code Llama',
    description: 'Specialized in programming help, code review, and technical problem-solving across multiple languages.',
    avatar_url: 'https://ui-avatars.com/api/?name=CL&background=7c3aed&color=fff&size=40&rounded=true',
    configuration: {
      model: 'codellama',
      system_prompt: 'You are an expert programming assistant. Focus on providing accurate, efficient code solutions and explanations. Always include comments in your code examples.'
    }
  },
  {
    name: 'Mistral',
    description: 'Efficient and capable model with strong performance across various tasks.',
    avatar_url: 'https://ui-avatars.com/api/?name=M&background=dc2626&color=fff&size=40&rounded=true',
    configuration: {
      model: 'mistral',
      system_prompt: 'You are a knowledgeable AI assistant. Provide concise yet comprehensive responses.'
    }
  },
  {
    name: 'Gemma',
    description: "Google's open model with excellent instruction following and creative capabilities.",
    avatar_url: 'https://ui-avatars.com/api/?name=G&background=ea580c&color=fff&size=40&rounded=true',
    configuration: {
      model: 'gemma',
      system_prompt: 'You are a creative and helpful AI assistant. Focus on being engaging and informative in your responses.'
    }
  },
  {
    name: 'WebScraper Mistral',
    description: 'Specialized AI agent that can analyze any website and answer questions about its content. Just provide a URL!',
    avatar_url: 'https://ui-avatars.com/api/?name=WS&background=8b5cf6&color=fff&size=40&rounded=true',
    configuration: {
      model: 'mistral',
      web_scraping_enabled: true,
      system_prompt: 'You are WebScraper Mistral, a specialized AI assistant with web scraping capabilities. When users provide URLs, you can access and analyze the website content to answer questions about it. You have the ability to read and understand web pages, extract key information, and provide detailed analysis of website content. Always be thorough in your analysis and cite specific information from the websites when answering questions.'
    }
  }
]

agents.each do |agent_data|
  agent = Agent.find_or_create_by(name: agent_data[:name]) do |a|
    a.description = agent_data[:description]
    a.avatar_url = agent_data[:avatar_url]
    a.configuration = agent_data[:configuration]
  end
  Rails.logger.debug { "Agent: #{agent.name} - #{agent.persisted? ? 'found' : 'created'}" }
end

Rails.logger.debug 'Seed completed successfully!'
