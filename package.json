{"name": "app", "private": true, "devDependencies": {"@eslint/js": "^9.27.0", "esbuild": "^0.25.4", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-formatter-compact": "^8.40.0", "eslint-plugin-prettier": "^5.4.0", "prettier": "^3.5.3", "stylelint": "^16.19.1", "stylelint-config-standard": "^38.0.0", "stylelint-config-tailwindcss": "^1.0.0"}, "scripts": {"build": "esbuild app/javascript/*.* --bundle --sourcemap --format=esm --outdir=app/assets/builds --public-path=/assets", "build:css": "npx @tailwindcss/cli -i ./app/assets/stylesheets/application.tailwind.css -o ./app/assets/builds/application.css --minify"}, "dependencies": {"@hotwired/stimulus": "^3.2.2", "@hotwired/turbo-rails": "^8.0.13", "@tailwindcss/cli": "^4.1.7", "tailwindcss": "^4.1.7"}}