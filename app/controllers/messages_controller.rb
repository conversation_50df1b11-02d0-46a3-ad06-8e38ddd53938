# frozen_string_literal: true

class MessagesController < ApplicationController
  before_action :set_chat
  before_action :set_user_cookie

  def create
    # Verify user has access to this chat
    unless @chat.user_cookie == @user_cookie
      redirect_to chats_path, alert: 'Access denied'
      return
    end

    @message = @chat.messages.new(message_params)
    @message.role = 'user'

    if @message.save
      broadcast_user_message
      broadcast_typing_start

      # Use web scraping job if agent has web scraping enabled
      if @chat.agent.configuration&.dig('web_scraping_enabled')
        WebScrapingAiResponseJob.perform_later(@chat.id, @message.content)
      else
        GenerateAiResponseJob.perform_later(@chat.id, @message.content)
      end

      head :ok
    else
      render json: { error: 'Failed to send message' }, status: :unprocessable_entity
    end
  end

  private

  def set_chat
    @chat = Chat.find(params[:chat_id])
  end

  def set_user_cookie
    @user_cookie = cookies[:user_id] ||= SecureRandom.uuid
  end

  def message_params
    params.expect(message: [:content])
  end

  def broadcast_user_message
    ActionCable.server.broadcast("chat_#{@chat.id}",
                                 { type: 'message',
                                   message: {
                                     id: @message.id,
                                     content: @message.content,
                                     role: @message.role,
                                     created_at: @message.created_at.strftime('%H:%M')
                                   } })
  end

  def broadcast_typing_start
    ActionCable.server.broadcast("chat_#{@chat.id}",
                                 { type: 'typing_start',
                                   agent_name: @chat.agent.name })
  end
end
