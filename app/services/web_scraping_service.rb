# frozen_string_literal: true

class WebScrapingService
  require 'mechanize'
  require 'uri'

  def initialize
    @agent = Mechanize.new
    @agent.user_agent = 'Mozilla/5.0 (compatible; ChatLamma WebScraper)'
    @agent.request_headers = {
      'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'Accept-Language' => 'en-US,en;q=0.5',
      'Accept-Encoding' => 'gzip, deflate',
      'Connection' => 'keep-alive'
    }

    # Set timeouts
    @agent.open_timeout = 10
    @agent.read_timeout = 30

    # Follow redirects (this is enabled by default in Mechanize)
    @agent.redirect_ok = true
  end

  def scrape_website(url)
    return { error: 'Invalid URL' } unless valid_url?(url)

    begin
      page = @agent.get(url)

      content = extract_content(page)

      {
        url: url,
        title: extract_title(page),
        content: content,
        summary: summarize_content(content),
        metadata: extract_metadata(page),
        scraped_at: Time.current
      }
    rescue Mechanize::ResponseCodeError => e
      Rails.logger.error "HTTP Error scraping #{url}: #{e.message}"
      { error: "Failed to access website: HTTP #{e.response_code}" }
    rescue Net::TimeoutError => e
      Rails.logger.error "Timeout scraping #{url}: #{e.message}"
      { error: "Website took too long to respond" }
    rescue SocketError => e
      Rails.logger.error "Network error scraping #{url}: #{e.message}"
      { error: "Could not connect to website" }
    rescue StandardError => e
      Rails.logger.error "Error scraping #{url}: #{e.message}"
      { error: "Failed to scrape website: #{e.message}" }
    end
  end

  def extract_urls_from_text(text)
    url_regex = %r{https?://[^\s<>"{}|\\^`\[\]]+}
    text.scan(url_regex).uniq
  end

  private

  def valid_url?(url)
    uri = URI.parse(url)
    uri.is_a?(URI::HTTP) || uri.is_a?(URI::HTTPS)
  rescue URI::InvalidURIError
    false
  end

  def extract_title(page)
    title = page.title&.strip
    return title if title.present?

    # Fallback to h1 tag
    h1 = page.search('h1').first
    h1&.text&.strip || 'Untitled Page'
  end

  def extract_content(page)
    # Remove script and style elements
    page.search('script, style, nav, footer, header, aside').remove

    # Try to find main content areas
    main_content = page.search('main, article, .content, .post, .entry, #content, #main').first

    if main_content
      extract_text_from_element(main_content)
    else
      # Fallback to body content
      body = page.search('body').first
      extract_text_from_element(body) if body
    end
  end

  def extract_text_from_element(element)
    # Get text content and clean it up
    text = element.text

    # Clean up whitespace
    text = text.gsub(/\s+/, ' ').strip

    # Remove excessive newlines
    text = text.gsub(/\n\s*\n/, "\n\n")

    text
  end

  def extract_metadata(page)
    metadata = {}

    # Extract meta description
    description = page.search('meta[name="description"]').first
    metadata[:description] = description['content'] if description

    # Extract meta keywords
    keywords = page.search('meta[name="keywords"]').first
    metadata[:keywords] = keywords['content'] if keywords

    # Extract Open Graph data
    og_title = page.search('meta[property="og:title"]').first
    metadata[:og_title] = og_title['content'] if og_title

    og_description = page.search('meta[property="og:description"]').first
    metadata[:og_description] = og_description['content'] if og_description

    # Extract headings structure
    headings = page.search('h1, h2, h3, h4, h5, h6').map(&:text).map(&:strip).reject(&:empty?)
    metadata[:headings] = headings.first(10) # Limit to first 10 headings

    metadata
  end

  def summarize_content(content)
    return '' if content.blank?

    # Simple summarization - take first few sentences and key points
    sentences = content.split(/[.!?]+/).map(&:strip).reject(&:empty?)

    # Take first 3 sentences as summary
    summary = sentences.first(3).join('. ')
    summary += '.' unless summary.end_with?('.')

    # Limit summary length
    summary.length > 500 ? summary[0..497] + '...' : summary
  end
end
