# frozen_string_literal: true

class WebScrapingAiResponseJob < ApplicationJob
  queue_as :default

  def perform(chat_id, user_message)
    chat = Chat.find(chat_id)
    previous_messages = chat.messages.ordered

    # Check if this agent has web scraping enabled
    if chat.agent.configuration&.dig('web_scraping_enabled')
      # Extract URLs from the user message
      scraping_service = WebScrapingService.new
      urls = scraping_service.extract_urls_from_text(user_message)

      if urls.any?
        # Scrape websites and enhance the conversation context
        scraped_data = scrape_websites(urls)
        enhanced_message = enhance_message_with_scraped_data(user_message, scraped_data)
        
        # Generate AI response with enhanced context
        ai_response = generate_enhanced_response(chat.agent, previous_messages, enhanced_message, scraped_data)
      else
        # No URLs found, generate normal response
        ai_response = generate_normal_response(chat.agent, previous_messages, user_message)
      end
    else
      # Agent doesn't have web scraping enabled, generate normal response
      ai_response = generate_normal_response(chat.agent, previous_messages, user_message)
    end

    # Create and save the AI response message
    response_message = chat.messages.create!(
      role: 'assistant',
      content: ai_response
    )

    # Broadcast typing stop and the AI response
    ActionCable.server.broadcast("chat_#{chat.id}", { type: 'typing_stop' })
    ActionCable.server.broadcast("chat_#{chat.id}",
                                 { type: 'message',
                                   message: {
                                     id: response_message.id,
                                     content: response_message.content,
                                     role: response_message.role,
                                     created_at: response_message.created_at.strftime('%H:%M')
                                   } })
  rescue StandardError => e
    Rails.logger.error "Web Scraping AI Response Job failed: #{e.message}"

    # Send error message to user
    error_message = chat.messages.create!(
      role: 'assistant',
      content: "I'm sorry, I encountered an error while processing your request. Please try again."
    )

    # Broadcast typing stop and error message
    ActionCable.server.broadcast("chat_#{chat.id}", { type: 'typing_stop' })
    ActionCable.server.broadcast("chat_#{chat.id}",
                                 { type: 'message',
                                   message: {
                                     id: error_message.id,
                                     content: error_message.content,
                                     role: error_message.role,
                                     created_at: error_message.created_at.strftime('%H:%M')
                                   } })
  end

  private

  def scrape_websites(urls)
    scraping_service = WebScrapingService.new
    scraped_data = []

    urls.each do |url|
      Rails.logger.info "Scraping website: #{url}"
      result = scraping_service.scrape_website(url)
      
      if result[:error]
        Rails.logger.error "Failed to scrape #{url}: #{result[:error]}"
        scraped_data << { url: url, error: result[:error] }
      else
        Rails.logger.info "Successfully scraped #{url}: #{result[:title]}"
        scraped_data << result
      end
    end

    scraped_data
  end

  def enhance_message_with_scraped_data(user_message, scraped_data)
    enhanced_message = user_message + "\n\n"
    enhanced_message += "=== WEBSITE CONTENT ANALYSIS ===\n"
    
    scraped_data.each do |data|
      if data[:error]
        enhanced_message += "URL: #{data[:url]} - Error: #{data[:error]}\n"
      else
        enhanced_message += "URL: #{data[:url]}\n"
        enhanced_message += "Title: #{data[:title]}\n"
        enhanced_message += "Summary: #{data[:summary]}\n"
        enhanced_message += "Content: #{data[:content][0..2000]}#{'...' if data[:content].length > 2000}\n"
        enhanced_message += "---\n"
      end
    end

    enhanced_message
  end

  def generate_enhanced_response(agent, messages, enhanced_message, scraped_data)
    # Build enhanced conversation with website context
    conversation = build_enhanced_conversation(agent, messages, enhanced_message, scraped_data)
    
    ollama_service = OllamaService.new
    ollama_service.generate_response_with_context(agent, conversation)
  end

  def generate_normal_response(agent, messages, user_message)
    ollama_service = OllamaService.new
    ollama_service.generate_response(agent, messages, user_message)
  end

  def build_enhanced_conversation(agent, messages, enhanced_message, scraped_data)
    conversation = ''

    # Add enhanced system prompt for web scraping
    system_prompt = agent.configuration&.dig('system_prompt') || ''
    system_prompt += "\n\nYou have access to real-time website content. Use this information to provide accurate, detailed responses about the websites mentioned."
    
    conversation += "System: #{system_prompt}\n\n"

    # Add website context
    conversation += "=== AVAILABLE WEBSITE DATA ===\n"
    scraped_data.each do |data|
      next if data[:error]
      
      conversation += "Website: #{data[:url]}\n"
      conversation += "Title: #{data[:title]}\n"
      conversation += "Description: #{data[:metadata][:description] || 'N/A'}\n"
      conversation += "Key Headings: #{data[:metadata][:headings]&.join(', ') || 'N/A'}\n"
      conversation += "Content Summary: #{data[:summary]}\n"
      conversation += "Full Content: #{data[:content]}\n"
      conversation += "---\n"
    end
    conversation += "=== END WEBSITE DATA ===\n\n"

    # Add previous messages
    messages.each do |message|
      role = message.role == 'user' ? 'Human' : 'Assistant'
      conversation += "#{role}: #{message.content}\n\n"
    end

    # Add current enhanced message
    conversation += "Human: #{enhanced_message}\n\nAssistant:"

    conversation
  end
end
